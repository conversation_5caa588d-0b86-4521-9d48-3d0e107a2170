# 9proxy Integration

This document explains how to integrate 9proxy service with the card crawler for enhanced proxy management and IP rotation.

## Overview

9proxy is a residential proxy service that provides high-quality IP addresses for web scraping. The integration allows the card crawler to automatically fetch and rotate proxies from your 9proxy service.

## Prerequisites

1. **9proxy Service**: You need to have 9proxy installed and running on your system
2. **9proxy API**: The 9proxy API must be enabled and accessible
3. **Valid 9proxy Account**: An active 9proxy subscription

## Setting up 9proxy

### 1. Install and Start 9proxy

Follow the 9proxy documentation to install the service on your system. For Linux:

```bash
# Start the 9proxy service
systemctl start 9proxyd.service

# Enable API functionality
9proxy api

# Set API port (optional, default port will be used)
9proxy api -p 8080

# Start the API
9proxy api -s
```

### 2. Get API Endpoint

After starting the API, note the endpoint URL displayed in the terminal. It's typically:
```
http://localhost:8080
```

## Integration Methods

### Method 1: Using CardCrawler Options (Recommended)

The simplest way to integrate 9proxy is through the CardCrawler options:

```typescript
import { CardCrawler } from './card-crawler.js'

const crawler = new CardCrawler(
  'your-host.com',
  'your-cookies',
  undefined, // No explicit proxy manager
  {
    nineProxyConfig: {
      apiEndpoint: 'http://localhost:8080',
      refreshInterval: 5 * 60 * 1000, // 5 minutes
      country: 'US', // Optional: filter by country
    }
  }
)

// The crawler automatically uses 9proxy
const cards = await crawler.searchCards({ country: 'US' })
```

### Method 2: Using NineProxyManager Directly

For more control, create a NineProxyManager instance:

```typescript
import { CardCrawler } from './card-crawler.js'
import { NineProxyManager } from './proxy-manager.js'

const proxyManager = new NineProxyManager({
  apiEndpoint: 'http://localhost:8080',
  refreshInterval: 5 * 60 * 1000,
  country: 'US',
  state: 'CA', // Optional: filter by state
})

const crawler = new CardCrawler(
  'your-host.com',
  'your-cookies',
  proxyManager
)

// Use the crawler...
const cards = await crawler.searchCards({ country: 'US' })

// Clean up when done
proxyManager.destroy()
```

## Configuration Options

### NineProxyConfig Interface

```typescript
interface NineProxyConfig {
  apiEndpoint: string        // Required: 9proxy API endpoint
  refreshInterval?: number   // Optional: Refresh interval in ms (default: 5 minutes)
  country?: string          // Optional: Filter proxies by country code
  state?: string           // Optional: Filter proxies by state
  city?: string            // Optional: Filter proxies by city
  isp?: string             // Optional: Filter proxies by ISP
}
```

### Configuration Examples

```typescript
// Basic configuration
const basicConfig = {
  apiEndpoint: 'http://localhost:8080'
}

// Advanced configuration with filtering
const advancedConfig = {
  apiEndpoint: 'http://localhost:8080',
  refreshInterval: 2 * 60 * 1000, // 2 minutes
  country: 'US',
  state: 'CA',
  city: 'Los Angeles'
}
```

## Features

### Automatic Proxy Rotation
- Proxies are automatically rotated for each request
- Failed proxies are handled gracefully
- Cached proxies are used when API is unavailable

### Periodic Refresh
- Proxies are refreshed automatically at configured intervals
- Fresh proxy lists ensure optimal performance
- Manual refresh is available via `forceRefresh()`

### Error Handling
- Graceful fallback to cached proxies on API failures
- Comprehensive error logging
- Automatic retry mechanisms

### Filtering Support
- Filter proxies by country, state, city, or ISP
- Customize proxy selection based on your needs
- Dynamic filtering through API parameters

## API Methods

### NineProxyManager Methods

```typescript
// Force refresh proxies from API
await proxyManager.forceRefresh()

// Get last refresh timestamp
const lastRefresh = proxyManager.getLastRefreshTime()

// Get proxy count
const count = proxyManager.count

// Get next proxy (inherited from ProxyManager)
const proxy = proxyManager.getNextProxy()

// Clean up resources
proxyManager.destroy()
```

## Best Practices

1. **Resource Cleanup**: Always call `destroy()` when done with NineProxyManager
2. **Refresh Intervals**: Set appropriate refresh intervals based on your usage
3. **Error Handling**: Monitor logs for API connection issues
4. **Proxy Filtering**: Use filtering to get proxies from desired locations
5. **Testing**: Test the integration with a small number of requests first

## Troubleshooting

### Common Issues

1. **API Connection Failed**
   - Ensure 9proxy service is running
   - Verify API endpoint URL
   - Check firewall settings

2. **No Proxies Returned**
   - Check your 9proxy account status
   - Verify filtering parameters
   - Ensure sufficient proxy quota

3. **Proxy Authentication Failed**
   - Verify 9proxy credentials
   - Check proxy format conversion
   - Monitor proxy rotation

### Debug Information

Enable debug logging to troubleshoot issues:

```typescript
// The integration uses console.warn for info messages
// and console.error for error messages
// Monitor the console output for debugging information
```

## Examples

See `src/examples/9proxy-integration.ts` for complete working examples of all integration methods.

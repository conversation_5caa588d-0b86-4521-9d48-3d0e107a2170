/**
 * Test suite for 9proxy integration
 * 
 * Note: These tests require a running 9proxy service with API enabled
 * They are designed to be run manually when 9proxy is available
 */

import { NineProxyManager, type NineProxyConfig } from '../proxy-manager.js'
import { CardCrawler } from '../card-crawler.js'

// Mock 9proxy configuration for testing
const mockConfig: NineProxyConfig = {
  apiEndpoint: 'http://localhost:8080',
  refreshInterval: 30000, // 30 seconds for testing
  country: 'US',
}

/**
 * Test NineProxyManager basic functionality
 */
async function testNineProxyManager() {
  console.warn('Testing NineProxyManager...')
  
  try {
    const proxyManager = new NineProxyManager(mockConfig)
    
    // Wait a moment for initial fetch
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    console.warn(`Proxy count: ${proxyManager.count}`)
    console.warn(`Last refresh: ${proxyManager.getLastRefreshTime()}`)
    
    // Test getting proxies
    const proxy1 = proxyManager.getNextProxy()
    const proxy2 = proxyManager.getNextProxy()
    
    console.warn(`Proxy 1: ${proxy1 ? 'Available' : 'None'}`)
    console.warn(`Proxy 2: ${proxy2 ? 'Available' : 'None'}`)
    
    // Test force refresh
    await proxyManager.forceRefresh()
    console.warn(`After force refresh - Proxy count: ${proxyManager.count}`)
    
    // Clean up
    proxyManager.destroy()
    console.warn('NineProxyManager test completed successfully')
    
  } catch (error) {
    console.error('NineProxyManager test failed:', error)
  }
}

/**
 * Test CardCrawler with 9proxy integration via options
 */
async function testCardCrawlerWith9Proxy() {
  console.warn('Testing CardCrawler with 9proxy integration...')
  
  try {
    const crawler = new CardCrawler(
      'example.com', // Mock host
      'test=cookie', // Mock cookies
      undefined,
      {
        title: 'test-9proxy',
        nineProxyConfig: mockConfig,
      }
    )
    
    // The crawler should have initialized with 9proxy
    console.warn('CardCrawler with 9proxy created successfully')
    
    // Note: We can't actually test crawling without a real target
    // but we can verify the integration doesn't break initialization
    
  } catch (error) {
    console.error('CardCrawler 9proxy integration test failed:', error)
  }
}

/**
 * Test CardCrawler with explicit NineProxyManager
 */
async function testCardCrawlerWithExplicitManager() {
  console.warn('Testing CardCrawler with explicit NineProxyManager...')
  
  try {
    const proxyManager = new NineProxyManager(mockConfig)
    
    const crawler = new CardCrawler(
      'example.com', // Mock host
      'test=cookie', // Mock cookies
      proxyManager,
      {
        title: 'test-explicit-9proxy',
      }
    )
    
    console.warn('CardCrawler with explicit NineProxyManager created successfully')
    
    // Clean up
    proxyManager.destroy()
    
  } catch (error) {
    console.error('CardCrawler explicit manager test failed:', error)
  }
}

/**
 * Test proxy format conversion
 */
function testProxyFormatConversion() {
  console.warn('Testing proxy format conversion...')
  
  // Mock 9proxy response data
  const mockProxyData = [
    { ip: '***********', port: 8080, username: 'user1', password: 'pass1' },
    { ip: '***********', port: 8081 }, // No auth
    { ip: '***********', port: 8082, username: 'user3', password: 'pass3' },
  ]
  
  // Test the conversion logic (this would be inside NineProxyManager)
  const convertedProxies = mockProxyData.map((proxy) => {
    const auth = proxy.username && proxy.password ? `${proxy.username}:${proxy.password}@` : ''
    return `http://${auth}${proxy.ip}:${proxy.port}`
  })
  
  const expectedResults = [
    '***********************************',
    'http://***********:8081',
    '***********************************',
  ]
  
  let success = true
  for (let i = 0; i < convertedProxies.length; i++) {
    if (convertedProxies[i] !== expectedResults[i]) {
      console.error(`Proxy format conversion failed at index ${i}:`)
      console.error(`Expected: ${expectedResults[i]}`)
      console.error(`Got: ${convertedProxies[i]}`)
      success = false
    }
  }
  
  if (success) {
    console.warn('Proxy format conversion test passed')
  } else {
    console.error('Proxy format conversion test failed')
  }
}

/**
 * Run all tests
 */
async function runTests() {
  console.warn('Starting 9proxy integration tests...')
  console.warn('Note: These tests require a running 9proxy service')
  console.warn('')
  
  // Test proxy format conversion (doesn't require 9proxy service)
  testProxyFormatConversion()
  console.warn('')
  
  // Test CardCrawler integrations (don't require actual API calls)
  await testCardCrawlerWith9Proxy()
  console.warn('')
  
  await testCardCrawlerWithExplicitManager()
  console.warn('')
  
  // Test NineProxyManager (requires actual 9proxy service)
  console.warn('Testing NineProxyManager (requires running 9proxy service):')
  await testNineProxyManager()
  
  console.warn('')
  console.warn('All tests completed!')
}

// Export for use in other modules
export {
  testNineProxyManager,
  testCardCrawlerWith9Proxy,
  testCardCrawlerWithExplicitManager,
  testProxyFormatConversion,
  runTests,
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runTests().catch(console.error)
}

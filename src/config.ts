import { loadConfig } from './utils.js'

// Load config once and cache it
let config: any = null

function getConfig() {
  if (!config) {
    config = loadConfig()
  }
  return config
}

export const TARGET_HOST = getConfig().targetHost || 'stashpatrick.gl'
export const SAMPLE_PROXIES = getConfig().sampleProxies || []
export const TELEGRAM_BOT_TOKEN = getConfig().telegramBotToken || ''
export const TELEGRAM_CHANNEL_ID = getConfig().telegramChannelId || ''
export const IS_DEBUG = getConfig().isDebug || false
export const IS_FAKE_CHECKOUT = getConfig().isFakeCheckout !== undefined ? getConfig().isFakeCheckout : true
export const USE_FAST_BUY = getConfig().useFastBuy || false
export const LIMIT_PER_RUN = getConfig().limitPerRun || 1
export const DELAY_PER_RUN = getConfig().delayPerRun || 5

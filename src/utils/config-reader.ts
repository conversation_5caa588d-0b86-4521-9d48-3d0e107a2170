/**
 * Utility functions for reading configuration from various sources
 */

import fs from 'node:fs/promises'
import path from 'node:path'
import { fileURLToPath } from 'node:url'
import type { NineProxyConfig } from '../proxy-manager.js'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

/**
 * Extract 9proxy configuration from HTML file
 * This function parses the HTML file to extract 9proxy endpoint and settings
 */
export async function extract9ProxyConfigFromHTML(htmlFilePath: string): Promise<NineProxyConfig | null> {
  try {
    const htmlContent = await fs.readFile(htmlFilePath, 'utf-8')
    
    // Look for 9proxy endpoint in the HTML content
    // This searches for the input field with id="nineProxyEndpoint" and extracts its value
    const endpointMatch = htmlContent.match(/id="nineProxyEndpoint"[^>]*value="([^"]*)"/)
    const endpoint = endpointMatch ? endpointMatch[1] : null
    
    if (!endpoint) {
      return null
    }
    
    // Extract other 9proxy settings
    const refreshIntervalMatch = htmlContent.match(/id="nineProxyRefreshInterval"[^>]*value="([^"]*)"/)
    const countryMatch = htmlContent.match(/id="nineProxyCountry"[^>]*value="([^"]*)"/)
    const stateMatch = htmlContent.match(/id="nineProxyState"[^>]*value="([^"]*)"/)
    const cityMatch = htmlContent.match(/id="nineProxyCity"[^>]*value="([^"]*)"/)
    
    const config: NineProxyConfig = {
      apiEndpoint: endpoint
    }
    
    // Add optional settings if they exist
    if (refreshIntervalMatch && refreshIntervalMatch[1]) {
      const minutes = parseInt(refreshIntervalMatch[1])
      if (!isNaN(minutes) && minutes > 0) {
        config.refreshInterval = minutes * 60 * 1000 // Convert to milliseconds
      }
    }
    
    if (countryMatch && countryMatch[1]) {
      config.country = countryMatch[1]
    }
    
    if (stateMatch && stateMatch[1]) {
      config.state = stateMatch[1]
    }
    
    if (cityMatch && cityMatch[1]) {
      config.city = cityMatch[1]
    }
    
    return config
    
  } catch (error) {
    console.warn('Failed to read 9proxy configuration from HTML file:', error)
    return null
  }
}

/**
 * Get the default HTML file path
 */
export function getDefaultHTMLPath(): string {
  return path.resolve(__dirname, '../public/index.html')
}

/**
 * Read 9proxy configuration from the default HTML file location
 */
export async function read9ProxyConfigFromDefaultHTML(): Promise<NineProxyConfig | null> {
  const htmlPath = getDefaultHTMLPath()
  return await extract9ProxyConfigFromHTML(htmlPath)
}

/**
 * Alternative method: Extract 9proxy configuration from a JSON config file
 * This provides a fallback method for configuration
 */
export async function read9ProxyConfigFromJSON(configPath?: string): Promise<NineProxyConfig | null> {
  try {
    const defaultConfigPath = path.resolve(__dirname, '../config.json')
    const filePath = configPath || defaultConfigPath
    
    const configContent = await fs.readFile(filePath, 'utf-8')
    const config = JSON.parse(configContent)
    
    if (config.nineProxyConfig && config.nineProxyConfig.apiEndpoint) {
      return config.nineProxyConfig as NineProxyConfig
    }
    
    return null
  } catch (error) {
    // Silently fail for JSON config as it's optional
    return null
  }
}

/**
 * Get 9proxy configuration from multiple sources with priority:
 * 1. Explicit configuration passed as parameter
 * 2. Configuration from JSON file
 * 3. Configuration from HTML file
 */
export async function get9ProxyConfig(
  explicitConfig?: NineProxyConfig,
  jsonConfigPath?: string,
  htmlFilePath?: string
): Promise<NineProxyConfig | null> {
  // 1. Use explicit configuration if provided
  if (explicitConfig && explicitConfig.apiEndpoint) {
    return explicitConfig
  }
  
  // 2. Try JSON configuration
  const jsonConfig = await read9ProxyConfigFromJSON(jsonConfigPath)
  if (jsonConfig) {
    return jsonConfig
  }
  
  // 3. Try HTML configuration
  const htmlPath = htmlFilePath || getDefaultHTMLPath()
  const htmlConfig = await extract9ProxyConfigFromHTML(htmlPath)
  if (htmlConfig) {
    return htmlConfig
  }
  
  return null
}

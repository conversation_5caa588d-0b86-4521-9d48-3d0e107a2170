#!/usr/bin/env node
import type { CardSearchParams } from './types.js'
import process from 'node:process'
import { CardCrawler } from './card-crawler.js'
import { DELAY_PER_RUN, IS_DEBUG, LIMIT_PER_RUN, SAMPLE_PROXIES, USE_FAST_BUY } from './config.js'
import { closeDatabase, initializeDatabase } from './db/index.js'
import { MessageEmitter } from './message-emitter.js'
import { ProxyManager } from './proxy-manager.js'
import { loadConfig } from './utils.js'

async function loadSearchParams(): Promise<CardSearchParams> {
  try {
    const config = loadConfig()

    if (config.searchParams) {
      if (IS_DEBUG) {
        console.warn('Loaded search parameters from config.json')
      }

      return config.searchParams
    }

    throw new Error('No search parameters found in config')
  }
  catch (error) {
    throw new Error(`Error loading config, using default parameters: ${error}`)
  }
}

async function main() {
  try {
    MessageEmitter.info('Initializing card crawler...')

    // Initialize database
    await initializeDatabase()
    MessageEmitter.success('Database initialized successfully')

    const config = loadConfig()
    MessageEmitter.info(`Starting card crawler for ${config.targetHost}`)

    // Initialize the proxy manager if proxies are available
    let proxyManager: ProxyManager | undefined
    if (SAMPLE_PROXIES.length > 0) {
      proxyManager = new ProxyManager(SAMPLE_PROXIES)
      MessageEmitter.info(`Configured ${proxyManager.count} proxies for rotation`)
    }

    // Initialize the card crawler with config options
    const cardCrawler = new CardCrawler(
      config.targetHost,
      config.cookies,
      proxyManager,
      {
        userAgent: config.userAgent,
        debug: true,
        debugDir: 'debug_html',
      },
    )
    MessageEmitter.success('Card crawler initialized successfully')

    // Load search parameters from config
    const searchParams = await loadSearchParams()
    MessageEmitter.info('Search parameters loaded from config')

    // Start the infinite crawling loop
    await excute(cardCrawler, searchParams)
  }
  catch (error) {
    const message = error instanceof Error ? error.message : error
    MessageEmitter.error(`Critical error in main function: ${message}`)
    console.error('Critical error in main function:', message)

    // Try to restart after a delay
    MessageEmitter.info('Attempting to restart in 10 seconds...')
    await delay(10)

    // Recursively restart the main function (don't close DB in this case)
    return main()
  }
}

async function excute(cardCrawler: CardCrawler, searchParams: CardSearchParams) {
  try {
    MessageEmitter.info('🔄 Starting new crawling iteration...')
    const cards = await cardCrawler.crawl(searchParams)

    if (cards.length === 0) {
      MessageEmitter.warn('No cards found. Waiting for next run...')
      await delay(DELAY_PER_RUN)
    }
    else {
      const selectedCards = cards.slice(0, LIMIT_PER_RUN)
      MessageEmitter.success(`Found ${cards.length} cards, selecting ${selectedCards.length} for purchase`)

      const t1 = Date.now()
      let purchaseSuccess = false
      let purchasedCards: typeof selectedCards = []

      if (USE_FAST_BUY) {
        MessageEmitter.info('Using Fast Buy workflow...')

        // Process cards one by one with Fast Buy
        for (const card of selectedCards) {
          MessageEmitter.info(`Fast Buy for card: ${card.id}`)
          const fastBuyResult = await cardCrawler.fastBuy(card.id as string)

          if (fastBuyResult.debugFile && IS_DEBUG) {
            MessageEmitter.info(`Fast Buy debug file saved to: ${fastBuyResult.debugFile}`)
          }

          if (fastBuyResult.success) {
            MessageEmitter.success(`Fast Buy successful for card: ${card.id}`)
            purchasedCards.push(card)
            purchaseSuccess = true
          }
          else {
            MessageEmitter.error(`Fast Buy failed for card ${card.id}: ${fastBuyResult.message}`)
          }
        }
      }
      else {
        MessageEmitter.info('Using traditional cart workflow...')
        MessageEmitter.info('Adding cards to cart...')
        const addToCartResult = await cardCrawler.addToCart(selectedCards.map(card => card.id as string))

        if (addToCartResult.debugFile && IS_DEBUG) {
          MessageEmitter.info(`Cart debug file saved to: ${addToCartResult.debugFile}`)
        }

        if (!addToCartResult.success) {
          MessageEmitter.error(`Failed to add cards to cart: ${addToCartResult.message}`)
          MessageEmitter.info('Waiting before next run...')
          await delay(3)
        }
        else {
          MessageEmitter.success('Cards added to cart successfully')
          MessageEmitter.info('Proceeding to checkout...')

          // Call the checkout method with the cards data
          const checkoutResult = await cardCrawler.checkout()

          if (checkoutResult.debugFile && IS_DEBUG) {
            MessageEmitter.info(`Checkout debug file saved to: ${checkoutResult.debugFile}`)
          }

          if (checkoutResult.success) {
            MessageEmitter.success(checkoutResult.message)
            purchasedCards = selectedCards
            purchaseSuccess = true
          }
          else {
            MessageEmitter.error(`Checkout failed: ${checkoutResult.message}`)
          }
        }
      }

      // Handle successful purchases
      if (purchaseSuccess && purchasedCards.length > 0) {
        MessageEmitter.success(`Successfully purchased ${purchasedCards.length} cards`)
        MessageEmitter.info('Saving purchased cards to database...')
        await cardCrawler.savePurchasedCardsToDatabase(purchasedCards)
        MessageEmitter.success('Cards saved to database successfully')
      }

      // Process completion
      const timeTaken = Date.now() - t1
      MessageEmitter.info(`✅ Iteration completed in ${timeTaken}ms`)
      MessageEmitter.info('⏱️ Waiting 3 seconds before next run...')
      await delay(3)
    }
  }
  catch (error) {
    const message = error instanceof Error ? error.message : error
    MessageEmitter.error(`❌ Error in crawling iteration: ${message}`)
    MessageEmitter.info('⏱️ Waiting 5 seconds before retrying...')
    await delay(5)
  }

  // Send heartbeat before next iteration
  MessageEmitter.success('💓 Crawler heartbeat - process is running')

  // Always continue the infinite loop
  await excute(cardCrawler, searchParams)
}

async function delay(time: number) {
  await new Promise(resolve => setTimeout(resolve, time * 1000))
}

// Graceful shutdown handling
process.on('SIGINT', () => {
  MessageEmitter.info('Received SIGINT, shutting down gracefully...')
  closeDatabase()
  process.exit(0)
})

process.on('SIGTERM', () => {
  MessageEmitter.info('Received SIGTERM, shutting down gracefully...')
  closeDatabase()
  process.exit(0)
})

process.on('uncaughtException', (error) => {
  MessageEmitter.error(`Uncaught exception: ${error.message}`)
  console.error('Uncaught exception:', error)
  closeDatabase()
  process.exit(1)
})

process.on('unhandledRejection', (reason, promise) => {
  MessageEmitter.error(`Unhandled rejection at: ${promise}, reason: ${reason}`)
  console.error('Unhandled rejection:', reason)
  closeDatabase()
  process.exit(1)
})

// Run the main function
main().catch((error) => {
  console.error('Main function failed:', error)
  closeDatabase()
  process.exit(1)
})

import type { NineProxyConfig, ProxyManager } from './proxy-manager.js'
import type { CardData, CardSearchParams } from './types.js'
import fs from 'node:fs/promises'
import path from 'node:path'
import { AdvancedCrawler } from './advanced-crawler.js'
import { parseCardTable, parsePagination } from './card-parser.js'
import { IS_DEBUG, IS_FAKE_CHECKOUT } from './config.js'
import { isCardPurchased, savePurchasedCard } from './db/index.js'
import { NineProxyManager } from './proxy-manager.js'
import { saveCardData } from './save/index.js'
import { isPassFilter } from './utils.js'

interface CardCrawlerOptions {
  title?: string
  debug?: boolean
  debugDir?: string
  userAgent?: string
  nineProxyConfig?: NineProxyConfig
}

// Default parameter values
export const DEFAULT_PARAMS = {
  minidep: 'ALL',
  cardtype: 'ALL',
  level: 'ALL',
  seller_rep: 'ALL',
  vendor: 'ALL',
  rating: 'ALL',
  valid: 'ALL',
  adress: 'ALL',
  box: 'ALL',
  ssn: 'ALL',
  dob: 'ALL',
  phone: 'ALL',
  holder: 'ALL',
  email: 'ALL',
  email_part: 'ALL',
  ip: 'ALL',
  pswrd: 'ALL',
  dl: 'ALL',
  cvv: 'ALL',
  page: 1,
  price_to: undefined,
  country: 'ALL',
}

// Default user agent if none specified
const DEFAULT_USER_AGENT = 'Mozilla/5.0 (X11; Linux x86_64; rv:137.0) Gecko/20100101 Firefox/137.0'

/**
 * Card Crawler - Specialized crawler for the cards/search endpoint
 */
export class CardCrawler {
  private crawler: AdvancedCrawler
  private baseHost: string
  private currentPage = 1
  private totalPages = 1
  private title: string
  private debugDir: string
  private userAgent: string
  private csrfToken: string = ''

  /**
   * Create a new Card Crawler
   */
  constructor(host: string, cookies: string, proxyManager?: ProxyManager, options: CardCrawlerOptions = {}) {
    this.baseHost = host
    this.title = options.title || 'cards'
    this.debugDir = options.debugDir || 'debug'
    this.userAgent = options.userAgent || DEFAULT_USER_AGENT

    // Initialize the advanced crawler
    this.crawler = new AdvancedCrawler(`https://${host}`)
    this.crawler.setCookies(cookies)

    // Set the referrer to the base site
    this.crawler.setReferrer(`https://${host}/cards/search`)

    // Set browser-like headers for the cards endpoint
    this.crawler.setHeaders({
      'User-Agent': this.userAgent,
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
      'Accept-Language': 'en-US,en;q=0.5',
      'Accept-Encoding': 'gzip, deflate, br',
      'Upgrade-Insecure-Requests': '1',
      'Sec-Fetch-Dest': 'document',
      'Sec-Fetch-Mode': 'navigate',
      'Sec-Fetch-Site': 'same-origin',
      'Sec-Fetch-User': '?1',
    })

    // Set up proxy manager - prioritize explicit proxyManager, then 9proxy config
    if (proxyManager) {
      this.crawler.setProxyManager(proxyManager)
    }
    else if (options.nineProxyConfig) {
      const nineProxyManager = new NineProxyManager(options.nineProxyConfig)
      this.crawler.setProxyManager(nineProxyManager)
    }
  }

  /**
   * Build a URL with the provided search parameters
   */
  private buildSearchUrl(params: CardSearchParams): string {
    const mergedParams = { ...DEFAULT_PARAMS, ...params }
    const acceptedParams = Object.keys(DEFAULT_PARAMS)

    const queryParams = new URLSearchParams()
    for (const [key, value] of Object.entries(mergedParams)) {
      if (key === 'country' && params.countries) {
        continue
      }

      if (acceptedParams.includes(key) && value !== undefined) {
        queryParams.append(key, value.toString())
      }
    }

    if (params.bins) {
      queryParams.append('bins', params.bins.join(','))
    }

    // Handle countries as separate country[] parameters
    if (params.countries && params.countries.length > 0) {
      params.countries.forEach((country) => {
        queryParams.append('country[]', country)
      })
    }

    return `/cards/search?${queryParams.toString()}`
  }

  /**
   * Save HTML response to a file for debugging
   */
  private async saveDebugHtml(html: string, url: string, prefix: string = 'response'): Promise<string> {
    if (!IS_DEBUG)
      return ''

    try {
      // Create debug directory if it doesn't exist
      await fs.mkdir(this.debugDir, { recursive: true })

      // Create a timestamped filename
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
      const sanitizedUrl = url.replace(/[^a-z0-9]/gi, '_').substring(0, 50)
      const filename = `${prefix}_${timestamp}_${sanitizedUrl}.html`
      const filePath = path.join(this.debugDir, filename)

      // Write the HTML to the file
      await fs.writeFile(filePath, html, 'utf8')
      if (IS_DEBUG) {
        console.warn(`Debug HTML saved to: ${filePath}`)
      }

      return filePath
    }
    catch (error) {
      console.error('Error saving debug HTML:', error)
      return ''
    }
  }

  /**
   * Extract CSRF Token from HTML response
   */
  private extractCsrfToken(html: string): string {
    try {
      // Look for meta tag with csrf-token
      const metaTagMatch = html.match(/<meta name="csrf-token" content="([^"]+)"/i)
      if (metaTagMatch && metaTagMatch[1]) {
        return metaTagMatch[1]
      }

      // Look for input hidden field with _token
      const inputMatch = html.match(/<input type="hidden" name="_token" value="([^"]+)"/i)
      if (inputMatch && inputMatch[1]) {
        return inputMatch[1]
      }

      // Try to extract from JavaScript variables
      const jsMatch = html.match(/var\s+csrfToken\s*=\s*['"]([^'"]+)['"]/i)
      if (jsMatch && jsMatch[1]) {
        return jsMatch[1]
      }

      console.warn('Could not extract CSRF token from HTML')
      return ''
    }
    catch (error) {
      console.error('Error extracting CSRF token:', error)
      return ''
    }
  }

  /**
   * Update crawler configuration
   */
  updateConfig(options: { userAgent?: string, cookies?: string }) {
    // Update user agent if provided
    if (options.userAgent) {
      this.userAgent = options.userAgent
      this.crawler.setHeaders({
        'User-Agent': options.userAgent,
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'same-origin',
        'Sec-Fetch-User': '?1',
      })
    }

    // Update cookies if provided
    if (options.cookies) {
      this.crawler.setCookies(options.cookies)
    }
  }

  /**
   * Crawl the cards/search endpoint with the provided parameters
   */
  async searchCards(params: CardSearchParams = {}): Promise<{
    cards: CardData[]
    pagination: {
      currentPage: number
      totalPages: number
    }
    authenticated: boolean
    reason?: string
    debugFile?: string
    csrfToken?: string
  }> {
    try {
      const searchPath = this.buildSearchUrl(params)
      const response = await this.crawler.crawlPage(searchPath)

      // Save the HTML response for debugging if enabled
      const debugFile = await this.saveDebugHtml(response.rawHtml, response.url)

      // Extract and save CSRF token
      this.csrfToken = this.extractCsrfToken(response.rawHtml)
      if (this.csrfToken && IS_DEBUG) {
        console.warn(`CSRF Token extracted: ${this.csrfToken.substring(0, 10)}...`)
      }

      // Check for authentication issues
      const authCheck = this.checkForAuthenticationIssues(response.rawHtml)
      if (!authCheck.isAuthenticated) {
        console.warn(`Authentication issue detected: ${authCheck.reason}`)
        return {
          cards: [],
          pagination: {
            currentPage: 0,
            totalPages: 0,
          },
          authenticated: false,
          reason: authCheck.reason,
          debugFile,
          csrfToken: this.csrfToken,
        }
      }

      // Parse the HTML to extract card data
      const rawCards = parseCardTable(response.rawHtml, response.url).filter(card => card.id)
      console.warn(`Founds ${rawCards.length} cards in the search`)

      const cards = [] as CardData[]
      for (const card of rawCards) {
        if (await isCardPurchased(card.id as string)) {
          console.warn(`Card ${card.id} already purchased`)
          continue
        }

        if (!isPassFilter(card, params)) {
          continue
        }

        cards.push(card)
      }

      console.warn(`Founds ${cards.length} cards to purchase`)

      // Parse pagination info
      const pagination = parsePagination(response.rawHtml)
      this.currentPage = pagination.currentPage
      this.totalPages = pagination.totalPages

      // Return the parsed card data
      return {
        cards,
        pagination: {
          currentPage: this.currentPage,
          totalPages: this.totalPages,
        },
        authenticated: true,
        debugFile,
        csrfToken: this.csrfToken,
      }
    }
    catch (error) {
      console.error('Error during card search:', error)
      throw error
    }
  }

  /**
   * Get CSRF token
   */
  getCsrfToken(): string {
    return this.csrfToken
  }

  /**
   * Add a card to the cart
   * @param cardId The ID of the card to add to the cart
   * @returns Response data from the server
   */
  async addToCart(cardIds: string[]): Promise<{ success: boolean, message: string, debugFile?: string }> {
    try {
      // Ensure we have a CSRF token
      if (!this.csrfToken) {
        console.warn('No CSRF token available, attempting to fetch one first')
        // Try to fetch CSRF token by hitting the search page
        const searchResult = await this.searchCards({ page: 1 })
        if (!searchResult.authenticated) {
          return {
            success: false,
            message: `Failed to authenticate: ${searchResult.reason}`,
            debugFile: searchResult.debugFile,
          }
        }
      }

      // Prepare form data
      const formData = new URLSearchParams()
      formData.append('_token', this.csrfToken)
      formData.append('cards[]', cardIds.join(','))

      // Set headers specifically for the cart request
      const originalHeaders = this.crawler.getHeaders()
      this.crawler.setHeaders({
        ...originalHeaders,
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
        'Accept': 'application/json, text/javascript, */*; q=0.01',
        'X-Requested-With': 'XMLHttpRequest',
        'Origin': `https://${this.baseHost}`,
        'Referer': `https://${this.baseHost}/cards/buy`,
      })

      // Make the POST request
      const response = await this.crawler.makePostRequest('/addtocart', formData.toString())

      // Reset headers back to original
      this.crawler.setHeaders(originalHeaders)

      // Save debug HTML if enabled
      const debugFile = await this.saveDebugHtml(
        typeof response.data === 'string' ? response.data : JSON.stringify(response.data),
        '/addtocart',
        'cart_response',
      )

      // Parse response
      let success = false
      let message = 'Unknown response from server'

      if (response.status >= 200 && response.status < 300) {
        success = true
        message = 'Card added to cart successfully'
      }
      else {
        message = `Server returned status code ${response.status}`
      }

      return {
        success,
        message,
        debugFile,
      }
    }
    catch (error) {
      console.error('Error adding card to cart:', error)
      return {
        success: false,
        message: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
      }
    }
  }

  /**
   * Check if the response indicates wrong credentials
   */
  private checkForAuthenticationIssues(html: string): { isAuthenticated: boolean, reason?: string } {
    // Check for login form indicators
    if (html.includes('<title>Patrick Market</title>') && html.includes('form class="login100-form validate-form"')) {
      return { isAuthenticated: false, reason: 'Login page detected, session expired or cookies invalid' }
    }

    // Check for captcha which may indicate a login challenge
    if (html.includes('input class="form-control form-control-lg" name="captcha"')) {
      return { isAuthenticated: false, reason: 'Captcha detected, authentication challenge present' }
    }

    return { isAuthenticated: true }
  }

  /**
   * Save search results to a JSON file
   */
  async crawl(params: CardSearchParams = {}): Promise<CardData[]> {
    try {
      const result = await this.searchCards(params)

      if (!result.authenticated) {
        throw new Error(`Authentication failure: ${result.reason}`)
      }

      if (IS_DEBUG) {
        await saveCardData(result.cards, this.title, result.pagination.currentPage)
      }

      return result.cards || []
    }
    catch (error) {
      console.error('Error saving results:', error)
      throw error
    }
  }

  /**
   * Fast Buy - Purchase a single card directly without adding to cart first
   * @param cardId The ID of the card to purchase directly
   * @returns Response data from the server
   */
  async fastBuy(cardId: string): Promise<{ success: boolean, message: string, debugFile?: string }> {
    try {
      console.warn(`Processing Fast Buy for card: ${cardId}`)

      // Ensure we have a CSRF token
      if (!this.csrfToken) {
        console.warn('No CSRF token available, attempting to fetch one first')
        // Try to fetch CSRF token by hitting the search page
        const searchResult = await this.searchCards({ page: 1 })
        if (!searchResult.authenticated) {
          return {
            success: false,
            message: `Failed to authenticate: ${searchResult.reason}`,
            debugFile: searchResult.debugFile,
          }
        }
      }

      // Prepare form data for Fast Buy
      const formData = new URLSearchParams()
      formData.append('_token', this.csrfToken)
      formData.append('card', cardId)

      // Set headers specifically for the Fast Buy request
      const originalHeaders = this.crawler.getHeaders()
      this.crawler.setHeaders({
        ...originalHeaders,
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json, text/javascript, */*; q=0.01',
        'X-Requested-With': 'XMLHttpRequest',
        'Origin': `https://${this.baseHost}`,
        'Referer': `https://${this.baseHost}/cards/buy`,
      })

      // Make the POST request to /quickbuy endpoint
      const response = await this.crawler.makePostRequest('/quickbuy', formData.toString())

      // Reset headers back to original
      this.crawler.setHeaders(originalHeaders)

      // Save debug HTML if enabled
      const debugFile = await this.saveDebugHtml(
        typeof response.data === 'string' ? response.data : JSON.stringify(response.data),
        '/quickbuy',
        'fastbuy_response',
      )

      // Parse response
      let success = false
      let message = 'Unknown response from server'

      if (response.status >= 200 && response.status < 300) {
        success = true
        message = 'Fast Buy completed successfully'
      }
      else {
        message = `Server returned status code ${response.status}`
      }

      return {
        success,
        message,
        debugFile,
      }
    }
    catch (error) {
      console.error('Error during Fast Buy:', error)
      return {
        success: false,
        message: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
      }
    }
  }

  /**
   * Process checkout after adding cards to cart
   * @returns Response data from the server
   */
  async checkout(): Promise<{ success: boolean, message: string, debugFile?: string }> {
    try {
      console.warn('Processing checkout...')

      // Set headers specifically for the checkout request
      const originalHeaders = this.crawler.getHeaders()
      this.crawler.setHeaders({
        ...originalHeaders,
        Accept: 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        Referer: `https://${this.baseHost}/cards/buy`,
      })

      // Execute checkout with dedicated method that handles redirects
      const checkoutResult = await this.crawler.executeCheckout('/checkout')

      // Log checkout details
      if (IS_DEBUG) {
        console.warn(`Checkout status: ${checkoutResult.status}`)
        console.warn(`Final URL: ${checkoutResult.finalUrl}`)
        if (checkoutResult.redirectChain.length > 1) {
          console.warn(`Redirect chain: ${checkoutResult.redirectChain.join(' -> ')}`)
        }
      }

      // Save debug HTML if enabled
      const debugFile = await this.saveDebugHtml(
        checkoutResult.html,
        checkoutResult.finalUrl,
        'checkout_response',
      )

      // Reset headers back to original
      this.crawler.setHeaders(originalHeaders)

      const isSuccess = checkoutResult.redirectChain.some(url => url.includes('/cards/my'))

      return {
        success: isSuccess,
        message: isSuccess ? 'Checkout successful' : 'Checkout failed',
        debugFile,
      }
    }
    catch (error) {
      console.error('Error during checkout:', error)
      return {
        success: false,
        message: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
      }
    }
  }

  /**
   * Save purchased cards to the database
   */
  async savePurchasedCardsToDatabase(cards: CardData[]): Promise<void> {
    try {
      for (const card of cards) {
        await savePurchasedCard(card)

        if (IS_DEBUG) {
          console.warn(`Saved purchase of card ${card.id} to database`)
        }
      }
    }
    catch (error) {
      console.error('Error saving purchased cards to database:', error)
    }
  }
}

import axios from 'axios'

/**
 * Configuration for 9proxy integration
 */
export interface NineProxyConfig {
  apiEndpoint: string
  refreshInterval?: number // in milliseconds, default 5 minutes
  country?: string
  state?: string
  city?: string
  isp?: string
}

/**
 * Response format from 9proxy API
 */
export interface NineProxyResponse {
  error: boolean
  message: string
  data: Array<{
    ip: string
    port: number
    username?: string
    password?: string
    country?: string
    state?: string
    city?: string
    isp?: string
  }>
}

/**
 * Simple proxy manager for rotating proxies when crawling
 */
export class ProxyManager {
  protected proxies: string[]
  protected currentIndex = 0

  /**
   * Create a new proxy manager with a list of proxies
   * @param proxies List of proxies in format: *****************************:port
   */
  constructor(proxies: string[] = []) {
    this.proxies = proxies
  }

  /**
   * Add a proxy to the rotation list
   */
  addProxy(proxy: string): void {
    this.proxies.push(proxy)
  }

  /**
   * Add multiple proxies to the rotation list
   */
  addProxies(proxies: string[]): void {
    this.proxies.push(...proxies)
  }

  /**
   * Get the next proxy in the rotation
   * @returns The next proxy URL or undefined if no proxies are available
   */
  getNextProxy(): string | undefined {
    if (this.proxies.length === 0) {
      return undefined
    }

    const proxy = this.proxies[this.currentIndex]
    this.currentIndex = (this.currentIndex + 1) % this.proxies.length
    return proxy
  }

  /**
   * Get the current number of available proxies
   */
  get count(): number {
    return this.proxies.length
  }
}

/**
 * 9proxy integration manager that fetches proxies from 9proxy API
 */
export class NineProxyManager extends ProxyManager {
  private config: NineProxyConfig
  private lastRefresh: number
  private refreshTimer?: NodeJS.Timeout
  private cachedProxies: string[]

  constructor(config: NineProxyConfig) {
    super([]) // Initialize with empty array
    this.config = {
      refreshInterval: 5 * 60 * 1000, // 5 minutes default
      ...config,
    }
    this.lastRefresh = 0
    this.cachedProxies = []

    // Start initial proxy fetch
    this.refreshProxies()

    // Set up periodic refresh
    this.startPeriodicRefresh()
  }

  private async refreshProxies(): Promise<void> {
    try {
      // Using console.warn for info messages to comply with linting rules
      console.warn('Fetching proxies from 9proxy API...')

      const params: any = {}
      if (this.config.country)
        params.country = this.config.country
      if (this.config.state)
        params.state = this.config.state
      if (this.config.city)
        params.city = this.config.city
      if (this.config.isp)
        params.isp = this.config.isp

      const response = await axios.get<NineProxyResponse>(`${this.config.apiEndpoint}/api/proxy`, {
        params,
        timeout: 10000, // 10 second timeout
      })

      if (response.data.error) {
        throw new Error(`9proxy API error: ${response.data.message}`)
      }

      // Convert 9proxy format to internal format (*****************************:port)
      const newProxies: string[] = response.data.data.map((proxy) => {
        const auth = proxy.username && proxy.password ? `${proxy.username}:${proxy.password}@` : ''
        return `http://${auth}${proxy.ip}:${proxy.port}`
      })

      if (newProxies.length === 0) {
        console.warn('No proxies returned from 9proxy API')
        return
      }

      // Update proxies
      this.proxies = newProxies
      this.cachedProxies = [...newProxies]
      this.lastRefresh = Date.now()

      console.warn(`Successfully fetched ${newProxies.length} proxies from 9proxy API`)
    }
    catch (error) {
      console.error('Failed to fetch proxies from 9proxy API:', error)

      // Use cached proxies if available
      if (this.cachedProxies.length > 0) {
        console.warn(`Using ${this.cachedProxies.length} cached proxies`)
        this.proxies = [...this.cachedProxies]
      }
    }
  }

  private startPeriodicRefresh(): void {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
    }

    this.refreshTimer = setInterval(() => {
      this.refreshProxies()
    }, this.config.refreshInterval!)
  }

  /**
   * Force refresh proxies from 9proxy API
   */
  public async forceRefresh(): Promise<void> {
    await this.refreshProxies()
  }

  /**
   * Get the last refresh time
   */
  public getLastRefreshTime(): Date {
    return new Date(this.lastRefresh)
  }

  /**
   * Clean up resources
   */
  public destroy(): void {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
      this.refreshTimer = undefined
    }
  }
}

import type { <PERSON><PERSON><PERSON>, SellerResult } from './types.js'
import * as cheerio from 'cheerio'
import { IS_DEBUG } from './config.js'

/**
 * Parse HTML content from cards/search page and extract table data
 */
export function parseCardTable(html: string, url: string): CardData[] {
  const $ = cheerio.load(html)
  const cards: CardData[] = []

  // Check if the table exists
  const tableExists = $('.table-responsive table').length > 0

  if (!tableExists) {
    console.warn('No cards found!')
    return []
  }

  // Find table and get headers
  const table = $('.table-responsive table')
  const headers: string[] = []

  // Extract headers from thead
  table.find('thead th').each((index, th) => {
    // Extract header text, convert to lowercase and replace spaces with underscores
    let header = $(th).text().trim().toLowerCase().replace(/\s+/g, '_')

    // Handle the checkbox column
    if (header === '' && $(th).find('input[type="checkbox"]').length) {
      header = 'cardbuy'
    }

    headers.push(header)
  })

  if (IS_DEBUG) {
    console.warn(`Found headers: ${headers.join(', ')}`)
  }

  // Find all table rows in tbody
  const tableRows = table.find('tbody > tr')
  if (IS_DEBUG) {
    console.warn(`Found ${tableRows.length} rows`)
  }

  // Process each row
  tableRows.each((rowIndex, row) => {
    try {
      const card: CardData = {
        seller: {},
        info: [],
      }

      // Process each cell in the row
      $(row).find('th, td').each((cellIndex, cell) => {
        // Get the corresponding header
        const header = headers[cellIndex]
        if (!header)
          return

        // Variables used in switch cases
        let dbLink, notice, reviewText, reviewMatch
        let infoHtml, infoItems, countryText, checkbox

        // Handle different columns based on their content
        switch (header) {
          case 'db': {
            dbLink = $(cell).find('a[href*="/cards/search"]').first()
            if (dbLink.length) {
              card.database = dbLink.text().trim().replace(/\s+/g, ' ')

              // Extract validity percentage from patterns like "VALID 75%"
              const validMatch = card.database.match(/VALID\s+(\d+)%/i)
              if (validMatch && validMatch[1]) {
                card.valid = Number.parseInt(validMatch[1], 10)
              }

              // Extract date from patterns like "[01 Sep 2025]"
              const dateMatch = card.database.match(/\[([^\]]+)\]/)
              if (dateMatch && dateMatch[1]) {
                card.date = dateMatch[1].trim()
              }
            }

            // Extract notice if present (e.g., "No billing")
            notice = $(cell).find('.text-danger').first()
            if (notice.length) {
              card.notice = notice.text().trim()
            }

            // Extract seller info
            const sellerInfo = $(cell).find('a[href*="/seller/"]').first()
            const link = sellerInfo.attr('href')
            if (sellerInfo) {
              card.seller = {
                id: extractSellerId(link || ''),
                name: sellerInfo.find('b').text().trim(),
                link,
                rating: $(cell).find('.text-warning + div').text().trim(),
                totalReviews: 0,
              }
              if (card.seller.id) {
                card.link = buildCardLink(card.seller.id, url)
              }

              // Extract total reviews
              reviewText = $(cell).find('span:contains("Total review:")').text()
              reviewMatch = reviewText.match(/Total review: (\d+)/)
              if (reviewMatch && reviewMatch[1]) {
                card.seller.totalReviews = Number.parseInt(reviewMatch[1], 10)
              }
            }
            break
          }

          case 'bin':
            card.bin = $(cell).text().trim()
            break

          case 'exp':
            card.exp = $(cell).text().trim()
            break

          case 'cvv':
            // Check for success icon
            card.cvv = $(cell).find('.text-success').length > 0
            break

          case 'type':
            card.type = $(cell).text().trim()
            break

          case 'vendor':
            card.vendor = $(cell).text().trim()
            break

          case 'lvl':
            card.lvl = $(cell).text().trim()
            break

          case 'bank':
            card.bank = $(cell).text().trim()
            break

          case 'country':
            // Extract country code
            countryText = $(cell).text().trim()
            card.country = countryText.replace(/\s+/g, '')
            break

          case 'city':
            card.city = $(cell).text().trim()
            break

          case 'state':
            card.state = $(cell).text().trim()
            break

          case 'zip':
            card.zip = $(cell).text().trim()
            break

          case 'info':
            // Split by <br> tags to extract individual info items
            infoHtml = $(cell).html()
            if (infoHtml) {
              infoItems = infoHtml.split('<br>')
              card.info = infoItems.map(item => $(item).text().trim()).filter(Boolean)
            }
            break

          case 'ref':
            // Check for success icon
            card.ref = $(cell).find('.text-success').length > 0
            break

          case 'price':
            card.price = $(cell).text().trim()
            break

          case 'cardbuy':
            // Extract card ID from checkbox value
            checkbox = $(cell).find('input.cardbuy')
            if (checkbox.length) {
              card.id = checkbox.val() as string
            }
            break
        }
      })

      // Add the card to the array
      expectedSellerData(card.seller)
      expectedCardData(card)
      cards.push(card)
    }
    catch (error) {
      console.error(`Error parsing row ${rowIndex}:`, error)
    }
  })

  return cards
}

/**
 * Parse pagination information to get total pages and current page
 */
export function parsePagination(html: string): { currentPage: number, totalPages: number } {
  const $ = cheerio.load(html)
  let currentPage = 1
  let totalPages = 1

  // Look for pagination links
  const pagination = $('.pagination')
  if (pagination.length) {
    // Get current page from active class
    const activePage = pagination.find('li.active')
    if (activePage.length) {
      currentPage = Number.parseInt(activePage.text().trim(), 10) || 1
      console.warn(`Current page: ${currentPage}`)
    }

    // Get total pages from the last page link
    const pageLinks = pagination.find('li a')
    if (pageLinks.length) {
      // Extract numbers from all pagination links
      const pageNumbers: number[] = []
      pageLinks.each((_, link) => {
        const pageText = $(link).text().trim()
        const pageNum = Number.parseInt(pageText, 10)
        if (!Number.isNaN(pageNum)) {
          pageNumbers.push(pageNum)
        }
      })

      // Get the highest page number
      if (pageNumbers.length > 0) {
        totalPages = Math.max(...pageNumbers)
      }

      console.warn(`Total pages: ${totalPages}`)
    }
  }
  else {
    if (IS_DEBUG) {
      console.warn('No pagination found')
    }
  }

  return { currentPage, totalPages }
}

function expectedSellerData(seller: SellerResult): void {
  if (!seller.name || !seller.link || !seller.rating || !seller.totalReviews) {
    throw new Error('Seller data is missing required fields, Please update the parser')
  }
}

function expectedCardData(card: CardData): void {
  if (!card.id) {
    throw new Error('Card data is missing required fields, Please update the parser')
  }
}

function extractSellerId(sellerLink: string): string {
  const url = new URL(sellerLink)
  return url.pathname.split('/').pop() || ''
}

function buildCardLink(sellerId: string, searchPath: string) {
  const url = new URL(searchPath)

  // Remove page parameter if it exists
  url.searchParams.delete('page')

  // Add seller ID parameter
  url.searchParams.append('seller[]', sellerId)

  // Return the final URL as a string
  return url.toString()
}

/**
 * Example of how to integrate 9proxy with the card crawler
 * 
 * This example shows how to:
 * 1. Configure 9proxy settings
 * 2. Use 9proxy with CardCrawler
 * 3. Handle 9proxy-specific options
 */

import { CardCrawler } from '../card-crawler.js'
import { NineProxyManager, type NineProxyConfig } from '../proxy-manager.js'

// Example 9proxy configuration
const nineProxyConfig: NineProxyConfig = {
  // The API endpoint of your local 9proxy service
  // This is typically http://localhost:PORT after starting 9proxy API
  apiEndpoint: 'http://localhost:8080',
  
  // Refresh interval in milliseconds (optional, default: 5 minutes)
  refreshInterval: 5 * 60 * 1000, // 5 minutes
  
  // Optional proxy filtering parameters
  country: 'US', // Filter by country code
  // state: 'CA', // Filter by state (optional)
  // city: 'Los Angeles', // Filter by city (optional)
  // isp: 'Comcast', // Filter by ISP (optional)
}

// Example 1: Using 9proxy with CardCrawler via options
async function example1() {
  const crawler = new CardCrawler(
    'example.com',
    'your-cookies-here',
    undefined, // No explicit proxy manager
    {
      title: 'cards-with-9proxy',
      nineProxyConfig: nineProxyConfig, // Use 9proxy configuration
    }
  )

  // The crawler will automatically create and use a NineProxyManager
  // Proxies will be fetched from 9proxy API and rotated automatically
  
  const cards = await crawler.searchCards({
    country: 'US',
    page: 1,
  })
  
  console.log(`Found ${cards.length} cards using 9proxy`)
}

// Example 2: Using NineProxyManager directly
async function example2() {
  // Create 9proxy manager directly
  const proxyManager = new NineProxyManager(nineProxyConfig)
  
  // Wait for initial proxy fetch
  await proxyManager.forceRefresh()
  
  console.log(`Loaded ${proxyManager.count} proxies from 9proxy`)
  
  const crawler = new CardCrawler(
    'example.com',
    'your-cookies-here',
    proxyManager, // Use explicit proxy manager
    {
      title: 'cards-with-explicit-9proxy',
    }
  )

  const cards = await crawler.searchCards({
    country: 'US',
    page: 1,
  })
  
  console.log(`Found ${cards.length} cards using explicit 9proxy manager`)
  
  // Clean up resources when done
  proxyManager.destroy()
}

// Example 3: Advanced 9proxy configuration
async function example3() {
  const advancedConfig: NineProxyConfig = {
    apiEndpoint: 'http://localhost:8080',
    refreshInterval: 2 * 60 * 1000, // Refresh every 2 minutes
    country: 'US',
    state: 'CA',
    city: 'Los Angeles',
  }
  
  const proxyManager = new NineProxyManager(advancedConfig)
  
  // Monitor proxy refresh
  console.log('Initial proxy fetch...')
  await proxyManager.forceRefresh()
  console.log(`Last refresh: ${proxyManager.getLastRefreshTime()}`)
  
  const crawler = new CardCrawler(
    'example.com',
    'your-cookies-here',
    proxyManager,
    {
      title: 'cards-advanced-9proxy',
    }
  )

  // Use the crawler...
  const cards = await crawler.searchCards({
    country: 'US',
    page: 1,
  })
  
  console.log(`Found ${cards.length} cards`)
  
  // Clean up
  proxyManager.destroy()
}

// Example usage
if (import.meta.url === `file://${process.argv[1]}`) {
  console.log('Running 9proxy integration examples...')
  
  // Uncomment the example you want to run:
  // await example1()
  // await example2()
  // await example3()
  
  console.log('Examples completed!')
}

export { example1, example2, example3 }

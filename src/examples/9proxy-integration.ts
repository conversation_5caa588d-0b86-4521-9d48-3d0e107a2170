/**
 * Example of how to integrate 9proxy with the card crawler
 *
 * This example shows how to:
 * 1. Configure 9proxy settings
 * 2. Use 9proxy with CardCrawler
 * 3. Handle 9proxy-specific options
 */

import type { NineProxyConfig } from '../proxy-manager.js'
import process from 'node:process'
import { CardCrawler } from '../card-crawler.js'

// Example 9proxy configuration
const nineProxyConfig: NineProxyConfig = {
  // The API endpoint of your local 9proxy service
  // This is typically http://localhost:PORT after starting 9proxy API
  apiEndpoint: 'http://localhost:8080',

  // Refresh interval in milliseconds (optional, default: 5 minutes)
  refreshInterval: 5 * 60 * 1000, // 5 minutes

  // Optional proxy filtering parameters
  country: 'US', // Filter by country code
  // state: 'CA', // Filter by state (optional)
  // city: 'Los Angeles', // Filter by city (optional)
  // isp: 'Comcast', // Filter by ISP (optional)
}

// Example 1: Using 9proxy with CardCrawler via options
async function example1() {
  const crawler = new CardCrawler(
    'example.com',
    'your-cookies-here',
    {
      title: 'cards-with-9proxy',
      nineProxyConfig, // Use 9proxy configuration
    },
  )

  // The crawler will automatically create and use a NineProxyManager
  // Proxies will be fetched from 9proxy API and rotated automatically

  const result = await crawler.searchCards({
    page: 1,
  })

  console.warn(`Found ${result.cards.length} cards using 9proxy`)
}

// Example 2: Using 9proxy configuration read from HTML file
async function example2() {
  // The crawler will automatically read 9proxy configuration from HTML file
  // if no explicit configuration is provided
  const crawler = new CardCrawler(
    'example.com',
    'your-cookies-here',
    {
      title: 'cards-with-html-9proxy',
    },
  )

  const result = await crawler.searchCards({
    page: 1,
  })

  console.warn(`Found ${result.cards.length} cards using 9proxy from HTML config`)
}

// Example 3: Advanced 9proxy configuration
async function example3() {
  const advancedConfig: NineProxyConfig = {
    apiEndpoint: 'http://localhost:8080',
    refreshInterval: 2 * 60 * 1000, // Refresh every 2 minutes
    country: 'US',
    state: 'CA',
    city: 'Los Angeles',
  }

  const crawler = new CardCrawler(
    'example.com',
    'your-cookies-here',
    {
      title: 'cards-advanced-9proxy',
      nineProxyConfig: advancedConfig,
    },
  )

  // Use the crawler...
  const result = await crawler.searchCards({
    page: 1,
  })

  console.warn(`Found ${result.cards.length} cards`)
}

// Example usage
if (import.meta.url === `file://${process.argv[1]}`) {
  console.warn('Running 9proxy integration examples...')

  // Uncomment the example you want to run:
  // await example1()
  // await example2()
  // await example3()

  console.warn('Examples completed!')
}

export { example1, example2, example3 }
